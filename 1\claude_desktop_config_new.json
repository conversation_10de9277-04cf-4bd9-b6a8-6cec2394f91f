{"mcpServers": {"memory-bank-mcp": {"command": "cmd", "args": ["/c", "npx", "-y", "@smithery/cli@latest", "run", "@alioshr/memory-bank-mcp", "--config", "{\"memoryBankRoot\":\"C:\\\\Users\\\\<USER>\\\\Downloads\\\\Tool admin\"}"]}, "filesystem": {"command": "node", "args": ["C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\@modelcontextprotocol\\server-filesystem\\dist\\index.js", "C:\\Users\\<USER>\\Downloads"]}, "word-document-server": {"command": "python", "args": ["C:\\Users\\<USER>\\Downloads\\new\\Office-Word-MCP-Server\\word_mcp_server.py"]}}}