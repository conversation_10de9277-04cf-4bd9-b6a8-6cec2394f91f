# Smithery configuration file: https://smithery.ai/docs/build/project-config

startCommand:
  type: stdio
  configSchema:
    # JSON Schema defining the configuration options for the MCP.
    type: object
    description: No configuration options required
  commandFunction:
    # A JS function that produces the CLI command based on the given config to start the MCP on stdio.
    |-
    (config) => ({command:'word_mcp_server', args:[]})
  exampleConfig: {}
