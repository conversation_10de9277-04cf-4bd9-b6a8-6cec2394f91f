# 更新Claude Desktop配置文件脚本
$configPath = "$env:APPDATA\Claude\claude_desktop_config.json"

Write-Host "正在更新Claude Desktop配置..."

$newConfig = @{
    mcpServers = @{
        "memory-bank-mcp" = @{
            command = "cmd"
            args = @(
                "/c",
                "npx",
                "-y",
                "@smithery/cli@latest",
                "run",
                "@alioshr/memory-bank-mcp",
                "--config",
                '{"memoryBankRoot":"C:\\Users\\<USER>\\Downloads\\Tool admin"}'
            )
        }
        "filesystem" = @{
            command = "node"
            args = @(
                "C:\Users\<USER>\AppData\Roaming\npm\node_modules\@modelcontextprotocol\server-filesystem\dist\index.js",
                "C:\Users\<USER>\Downloads"
            )
        }
        "word-document-server" = @{
            command = "python"
            args = @(
                "C:\Users\<USER>\Downloads\new\Office-Word-MCP-Server\word_mcp_server.py"
            )
        }
    }
}

# 转换为JSON并写入文件
$jsonConfig = $newConfig | ConvertTo-Json -Depth 10
$jsonConfig | Out-File -FilePath $configPath -Encoding UTF8

Write-Host "Claude Desktop配置已更新成功！"
Write-Host "配置文件位置: $configPath"
Write-Host ""
Write-Host "已添加的MCP服务器："
Write-Host "- memory-bank-mcp: 内存银行服务器"
Write-Host "- filesystem: 文件系统服务器"
Write-Host "- word-document-server: Office Word文档服务器"
Write-Host ""
Write-Host "请重启Claude Desktop以应用新配置！" 