#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@file data_validation.py - 数据验证脚本
@function 验证处理后的CSV文件数据质量
"""

import pandas as pd
from collections import Counter
import re

def validate_csv_data():
    """
    @function validate_csv_data - 验证CSV数据质量
    @returns {None}
    """
    csv_file = r"C:\Users\<USER>\Downloads\new\insurance_updated.csv"
    
    try:
        # 读取CSV文件
        df = pd.read_csv(csv_file)
        
        print("=" * 80)
        print("数据验证报告")
        print("=" * 80)
        
        # 基本统计信息
        print(f"总记录数: {len(df)}")
        print(f"总列数: {len(df.columns)}")
        print(f"列名: {list(df.columns)}")
        
        print("\n" + "-" * 60)
        print("数据完整性检查")
        print("-" * 60)
        
        # 检查每列的空值情况
        for col in df.columns:
            null_count = df[col].isnull().sum()
            empty_count = (df[col] == '').sum()
            total_missing = null_count + empty_count
            percentage = (total_missing / len(df)) * 100
            print(f"{col:25}: {total_missing:3d} 缺失 ({percentage:5.1f}%)")
        
        print("\n" + "-" * 60)
        print("姓名字段分析")
        print("-" * 60)
        
        # 检查姓名
        valid_names = df[df['Name'].notna() & (df['Name'] != '')]['Name']
        print(f"有效姓名数量: {len(valid_names)}")
        print(f"姓名示例:")
        for i, name in enumerate(valid_names.head(10)):
            print(f"  {i+1:2d}. {name}")
        
        print("\n" + "-" * 60)
        print("出生日期格式分析")
        print("-" * 60)
        
        # 检查出生日期格式
        birth_dates = df[df['Date of birth'].notna() & (df['Date of birth'] != '')]['Date of birth']
        print(f"有效出生日期数量: {len(birth_dates)}")
        
        # 分析日期格式
        date_formats = []
        for date in birth_dates.head(20):
            date_str = str(date)
            if '-' in date_str:
                date_formats.append('YYYY-MM-DD')
            elif '/' in date_str:
                date_formats.append('YYYY/MM/DD')
            else:
                date_formats.append('其他格式')
        
        format_counts = Counter(date_formats)
        print("日期格式分布:")
        for fmt, count in format_counts.items():
            print(f"  {fmt}: {count} 个")
        
        print("\n" + "-" * 60)
        print("护照号码分析")
        print("-" * 60)
        
        # 检查护照号码
        passports = df[df['passport'].notna() & (df['passport'] != '')]['passport']
        print(f"有效护照号码数量: {len(passports)}")
        print(f"护照号码示例:")
        for i, passport in enumerate(passports.head(10)):
            print(f"  {i+1:2d}. {passport}")
        
        print("\n" + "-" * 60)
        print("旅行日期分析")
        print("-" * 60)
        
        # 检查旅行日期
        travel_dates = df['Travel Date'].value_counts()
        print("旅行日期分布 (前10个):")
        for date, count in travel_dates.head(10).items():
            print(f"  {date}: {count} 人")
        
        # 检查月份分布
        months = []
        for date in df['Travel Date']:
            if 'February' in str(date):
                months.append('2月')
            elif 'March' in str(date):
                months.append('3月')
            elif 'April' in str(date):
                months.append('4月')
            else:
                months.append('其他')
        
        month_counts = Counter(months)
        print(f"\n月份分布:")
        for month, count in month_counts.items():
            print(f"  {month}: {count} 人")
        
        print("\n" + "-" * 60)
        print("目的地分析")
        print("-" * 60)
        
        # 检查目的地分布
        destinations = df['Destination'].value_counts()
        print("目的地分布:")
        for dest, count in destinations.items():
            print(f"  {dest}: {count} 人")
        
        print("\n" + "-" * 60)
        print("数据质量总结")
        print("-" * 60)
        
        # 计算数据质量指标
        total_records = len(df)
        complete_records = len(df[(df['Name'].notna()) & (df['Name'] != '') & 
                                 (df['Date of birth'].notna()) & (df['Date of birth'] != '')])
        
        quality_score = (complete_records / total_records) * 100
        
        print(f"总记录数: {total_records}")
        print(f"完整记录数 (有姓名和出生日期): {complete_records}")
        print(f"数据完整度: {quality_score:.1f}%")
        
        # 检查重复记录
        duplicates = df.duplicated(subset=['Name', 'Date of birth']).sum()
        print(f"重复记录数: {duplicates}")
        
        print("\n" + "=" * 80)
        print("验证完成!")
        print("=" * 80)
        
    except Exception as e:
        print(f"验证过程中出错: {str(e)}")

def main():
    """
    @function main - 主函数
    @returns {None}
    """
    validate_csv_data()

if __name__ == "__main__":
    main()
