#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@file document_validation.py - 验证生成的保险文档质量
@function 验证Excel文档和CSV文件的数据质量和完整性
"""

import pandas as pd
import os
from collections import Counter

def validate_documents():
    """
    @function validate_documents - 验证生成的文档
    @returns {None}
    """
    csv_file = r"C:\Users\<USER>\Downloads\new\insurance_updated_complete.csv"
    excel_file = r"C:\Users\<USER>\Downloads\new\Travel_Insurance_Backup_Documents.xlsx"
    
    print("=" * 80)
    print("旅游责任险备案文档验证报告")
    print("=" * 80)
    
    # 验证CSV文件
    if os.path.exists(csv_file):
        print(f"\n✅ CSV文件存在: {os.path.basename(csv_file)}")
        
        try:
            df = pd.read_csv(csv_file)
            print(f"📊 总记录数: {len(df)}")
            print(f"📊 总列数: {len(df.columns)}")
            
            # 检查数据完整性
            print(f"\n📋 数据完整性检查:")
            for col in df.columns:
                null_count = df[col].isnull().sum()
                empty_count = (df[col] == '').sum()
                total_missing = null_count + empty_count
                percentage = (total_missing / len(df)) * 100
                status = "✅" if percentage < 10 else "⚠️" if percentage < 50 else "❌"
                print(f"  {status} {col:20}: {len(df) - total_missing:3d} 完整 ({100-percentage:5.1f}%)")
            
            # 检查邮箱格式
            print(f"\n📧 邮箱地址质量检查:")
            valid_emails = df[df['email'].str.contains('@', na=False)]['email']
            print(f"  有效邮箱数量: {len(valid_emails)} / {len(df)}")
            
            # 邮箱域名分布
            if len(valid_emails) > 0:
                domains = [email.split('@')[1] for email in valid_emails if '@' in str(email)]
                domain_counts = Counter(domains)
                print(f"  邮箱域名分布 (前5个):")
                for domain, count in domain_counts.most_common(5):
                    print(f"    {domain}: {count} 个")
            
            # 检查手机号码格式
            print(f"\n📱 手机号码质量检查:")
            valid_phones = df[df['contact'].str.contains('+', na=False)]['contact']
            print(f"  有效手机号码数量: {len(valid_phones)} / {len(df)}")
            
            # 国家代码分布
            if len(valid_phones) > 0:
                country_codes = [phone.split(' ')[0] for phone in valid_phones if '+' in str(phone)]
                code_counts = Counter(country_codes)
                print(f"  国家代码分布:")
                for code, count in code_counts.most_common():
                    print(f"    {code}: {count} 个")
            
            # 性别分布
            print(f"\n👥 性别分布:")
            gender_counts = df['gendre'].value_counts()
            for gender, count in gender_counts.items():
                percentage = (count / len(df)) * 100
                print(f"  {gender}: {count} 人 ({percentage:.1f}%)")
            
            # 旅行月份分布
            print(f"\n📅 旅行月份分布:")
            month_counts = {}
            for date in df['Travel Date']:
                if 'February' in str(date):
                    month_counts['February'] = month_counts.get('February', 0) + 1
                elif 'March' in str(date):
                    month_counts['March'] = month_counts.get('March', 0) + 1
                elif 'April' in str(date):
                    month_counts['April'] = month_counts.get('April', 0) + 1
            
            for month, count in month_counts.items():
                percentage = (count / len(df)) * 100
                print(f"  {month} 2025: {count} 人 ({percentage:.1f}%)")
            
            # 目的地分布
            print(f"\n🏙️ 目的地分布:")
            dest_counts = df['Destination'].value_counts()
            for dest, count in dest_counts.items():
                percentage = (count / len(df)) * 100
                print(f"  {dest}: {count} 人 ({percentage:.1f}%)")
                
        except Exception as e:
            print(f"❌ 读取CSV文件时出错: {str(e)}")
    else:
        print(f"❌ CSV文件不存在: {csv_file}")
    
    # 验证Excel文件
    if os.path.exists(excel_file):
        print(f"\n✅ Excel文件存在: {os.path.basename(excel_file)}")
        
        try:
            # 读取Excel文件的所有工作表
            excel_data = pd.read_excel(excel_file, sheet_name=None)
            
            print(f"📊 工作表数量: {len(excel_data)}")
            print(f"📊 工作表列表:")
            
            total_customers = 0
            for sheet_name, sheet_df in excel_data.items():
                customer_count = len(sheet_df)
                total_customers += customer_count
                print(f"  📄 {sheet_name}: {customer_count} 位客户")
                
                # 检查列结构
                expected_columns = ['Full Name', 'Gender', 'Email Address', 'Contact Number', 
                                  'Date of Birth', 'Passport Number', 'Travel Date', 'Destination']
                actual_columns = list(sheet_df.columns)
                
                if actual_columns == expected_columns:
                    print(f"    ✅ 列结构正确")
                else:
                    print(f"    ⚠️ 列结构异常")
                    print(f"    期望: {expected_columns}")
                    print(f"    实际: {actual_columns}")
            
            print(f"\n📊 Excel总客户数: {total_customers}")
            
        except Exception as e:
            print(f"❌ 读取Excel文件时出错: {str(e)}")
    else:
        print(f"❌ Excel文件不存在: {excel_file}")
    
    print(f"\n" + "=" * 80)
    print("验证完成!")
    print("=" * 80)

def main():
    """
    @function main - 主函数
    @returns {None}
    """
    validate_documents()

if __name__ == "__main__":
    main()
