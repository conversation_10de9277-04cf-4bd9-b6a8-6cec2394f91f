#!/usr/bin/env python3
"""
Office Word MCP Server 安装测试脚本
"""

import sys
import os

def test_dependencies():
    """测试所需依赖是否正确安装"""
    print("=== 依赖检查 ===")
    
    required_modules = [
        'mcp',
        'docx',
        'msoffcrypto',
        'docx2pdf'
    ]
    
    missing_modules = []
    
    for module in required_modules:
        try:
            if module == 'docx':
                import docx
                print(f"✓ python-docx: {docx.__version__}")
            elif module == 'mcp':
                import mcp
                print(f"✓ mcp: 已安装")
            elif module == 'msoffcrypto':
                import msoffcrypto
                print(f"✓ msoffcrypto-tool: 已安装")
            elif module == 'docx2pdf':
                import docx2pdf
                print(f"✓ docx2pdf: 已安装")
        except ImportError:
            missing_modules.append(module)
            print(f"✗ {module}: 未安装")
    
    return len(missing_modules) == 0

def test_mcp_server():
    """测试MCP服务器文件"""
    print("\n=== MCP服务器文件检查 ===")
    
    server_file = "word_mcp_server.py"
    if os.path.exists(server_file):
        print(f"✓ {server_file}: 存在")
        return True
    else:
        print(f"✗ {server_file}: 不存在")
        return False

def test_claude_config():
    """测试Claude Desktop配置"""
    print("\n=== Claude Desktop配置检查 ===")
    
    config_path = os.path.expandvars(r"%APPDATA%\Claude\claude_desktop_config.json")
    
    if os.path.exists(config_path):
        print(f"✓ 配置文件存在: {config_path}")
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                content = f.read()
                if 'word-document-server' in content:
                    print("✓ Office Word MCP Server配置已添加")
                    return True
                else:
                    print("✗ Office Word MCP Server配置未找到")
                    return False
        except Exception as e:
            print(f"✗ 读取配置文件失败: {e}")
            return False
    else:
        print(f"✗ 配置文件不存在: {config_path}")
        return False

def main():
    """主测试函数"""
    print("Office Word MCP Server 安装测试")
    print("=" * 50)
    
    # 测试依赖
    deps_ok = test_dependencies()
    
    # 测试服务器文件
    server_ok = test_mcp_server()
    
    # 测试Claude配置
    config_ok = test_claude_config()
    
    print("\n=== 测试结果汇总 ===")
    
    if deps_ok and server_ok and config_ok:
        print("🎉 所有测试通过！Office Word MCP Server安装成功！")
        print("\n下一步操作：")
        print("1. 重启Claude Desktop")
        print("2. 在Claude中测试Word文档操作功能")
        print("3. 尝试命令如：'帮我分析当前目录的Word文档'")
        return True
    else:
        print("❌ 部分测试失败，请检查上述错误信息")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 