#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@file detailed_excel_reader.py - 详细读取Excel文件内容的工具脚本
@function 详细读取并显示Excel文件的完整内容
"""

import pandas as pd
import os

def display_excel_content(file_path, max_rows=50):
    """
    @function display_excel_content - 详细显示Excel文件内容
    @param {str} file_path - Excel文件路径
    @param {int} max_rows - 最大显示行数
    @returns {bool} 是否成功读取
    """
    try:
        print(f"\n{'='*80}")
        print(f"文件: {os.path.basename(file_path)}")
        print(f"{'='*80}")
        
        # 读取Excel文件
        excel_file = pd.ExcelFile(file_path)
        
        for sheet_name in excel_file.sheet_names:
            print(f"\n工作表: {sheet_name}")
            print("-" * 60)
            
            df = pd.read_excel(file_path, sheet_name=sheet_name)
            
            print(f"数据维度: {df.shape[0]} 行 × {df.shape[1]} 列")
            print(f"列名:")
            for i, col in enumerate(df.columns, 1):
                print(f"  {i:2d}. {col}")
            
            print(f"\n数据内容 (显示前 {min(max_rows, df.shape[0])} 行):")
            print("-" * 60)
            
            # 设置pandas显示选项
            pd.set_option('display.max_columns', None)
            pd.set_option('display.width', None)
            pd.set_option('display.max_colwidth', 50)
            
            # 显示数据
            display_df = df.head(max_rows)
            print(display_df.to_string(index=True))
            
            if df.shape[0] > max_rows:
                print(f"\n... (还有 {df.shape[0] - max_rows} 行数据未显示)")
                
        return True
        
    except Exception as e:
        print(f"读取文件时出错: {str(e)}")
        return False

def main():
    """
    @function main - 主函数
    @returns {None}
    """
    # Excel文件路径
    excel_files = [
        r"C:\Users\<USER>\Downloads\new\bookinglist_2025-03-13-000000-2025-06-13-235959_20250613145942_28209_4076.xlsx",
        r"C:\Users\<USER>\Downloads\new\bookinglist_2025-03-13-000000-2025-06-13-235959_20250613150003_28209_1160.xlsx"
    ]
    
    print("详细读取Excel文件内容...")
    
    for file_path in excel_files:
        if os.path.exists(file_path):
            display_excel_content(file_path, max_rows=20)  # 显示前20行
        else:
            print(f"文件不存在: {file_path}")
    
    # 也读取CSV文件
    csv_file = r"C:\Users\<USER>\Downloads\new\insurance.csv"
    if os.path.exists(csv_file):
        print(f"\n{'='*80}")
        print(f"CSV文件: {os.path.basename(csv_file)}")
        print(f"{'='*80}")
        
        try:
            df_csv = pd.read_csv(csv_file)
            print(f"数据维度: {df_csv.shape[0]} 行 × {df_csv.shape[1]} 列")
            print(f"列名:")
            for i, col in enumerate(df_csv.columns, 1):
                print(f"  {i:2d}. {col}")
            
            print(f"\n前20行数据:")
            print("-" * 60)
            pd.set_option('display.max_columns', None)
            print(df_csv.head(20).to_string(index=True))
            
            if df_csv.shape[0] > 20:
                print(f"\n... (还有 {df_csv.shape[0] - 20} 行数据未显示)")
                
        except Exception as e:
            print(f"读取CSV文件时出错: {str(e)}")
    
    print(f"\n{'='*80}")
    print("所有文件读取完成")
    print(f"{'='*80}")

if __name__ == "__main__":
    main()
