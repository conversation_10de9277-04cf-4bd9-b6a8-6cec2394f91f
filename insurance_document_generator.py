#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@file insurance_document_generator.py - 旅游责任险备案文档生成器
@function 基于客户数据生成分页的旅游责任险备案文档
"""

import pandas as pd
import random
import re
from collections import defaultdict
import string

class InsuranceDocumentGenerator:
    """
    @class InsuranceDocumentGenerator - 保险文档生成器
    """
    
    def __init__(self):
        """
        @function __init__ - 初始化生成器
        """
        self.csv_file = r"C:\Users\<USER>\Downloads\new\insurance_updated.csv"
        self.output_excel = r"C:\Users\<USER>\Downloads\new\Travel_Insurance_Backup_Documents.xlsx"
        self.output_csv = r"C:\Users\<USER>\Downloads\new\insurance_updated_complete.csv"
        
        # 邮箱服务商列表
        self.email_providers = [
            'gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com',
            'yahoo.com.hk', 'yahoo.com.tw', 'yahoo.com.sg',
            'live.com', 'icloud.com', 'protonmail.com'
        ]
        
        # 常见英文名性别映射
        self.male_names = {
            'john', 'michael', 'david', 'james', 'robert', 'william', 'richard', 'charles',
            'joseph', 'thomas', 'christopher', 'daniel', 'paul', 'mark', 'donald', 'steven',
            'andrew', 'joshua', 'kenneth', 'kevin', 'brian', 'george', 'timothy', 'ronald',
            'jason', 'edward', 'jeffrey', 'ryan', 'jacob', 'gary', 'nicholas', 'eric',
            'jonathan', 'stephen', 'larry', 'justin', 'scott', 'brandon', 'benjamin',
            'samuel', 'frank', 'raymond', 'alexander', 'patrick', 'jack', 'dennis',
            'jerry', 'tyler', 'aaron', 'henry', 'douglas', 'peter', 'nathan', 'noah',
            'lance', 'christopher', 'benjamin', 'keith', 'timothy', 'alfred', 'karl',
            'leo', 'jason', 'darren', 'jasper', 'henry', 'frederick', 'michael'
        }
        
        self.female_names = {
            'mary', 'patricia', 'jennifer', 'linda', 'elizabeth', 'barbara', 'susan',
            'jessica', 'sarah', 'karen', 'nancy', 'lisa', 'betty', 'helen', 'sandra',
            'donna', 'carol', 'ruth', 'sharon', 'michelle', 'laura', 'sarah', 'kimberly',
            'deborah', 'dorothy', 'lisa', 'nancy', 'karen', 'betty', 'helen', 'sandra',
            'donna', 'carol', 'ruth', 'sharon', 'michelle', 'laura', 'emily', 'melissa',
            'angela', 'kathleen', 'amy', 'anna', 'brenda', 'emma', 'olivia', 'cynthia',
            'marie', 'janet', 'catherine', 'frances', 'christine', 'samantha', 'debra',
            'rachel', 'carolyn', 'janet', 'virginia', 'maria', 'heather', 'diane',
            'julie', 'joyce', 'victoria', 'kelly', 'christina', 'joan', 'evelyn',
            'judith', 'andrea', 'hannah', 'jacqueline', 'martha', 'gloria', 'teresa',
            'sara', 'janice', 'marie', 'julia', 'kathryn', 'sophia', 'frances',
            'melissa', 'phoo', 'joana', 'ruth', 'mary', 'sheila', 'rachel', 'christy',
            'katie', 'kelly', 'angelita', 'nashita', 'kazi', 'sue', 'angie', 'dewi',
            'sara', 'liz', 'carmen', 'pamela', 'charlotte', 'sienna'
        }
        
        # 国家代码和手机号码格式
        self.country_phone_formats = {
            'MY': '+60 1{8}',  # Malaysia
            'SG': '+65 {8}',   # Singapore
            'HK': '+852 {8}',  # Hong Kong
            'TW': '+886 {9}',  # Taiwan
            'PH': '+63 9{9}',  # Philippines
            'KR': '+82 10{8}', # South Korea
            'JP': '+81 {10}',  # Japan
            'VN': '+84 {9}',   # Vietnam
            'ID': '+62 8{8}',  # Indonesia
            'TH': '+66 {9}',   # Thailand
            'CN': '+86 1{10}', # China
            'US': '+1 {10}',   # United States
            'UK': '+44 7{9}',  # United Kingdom
            'AU': '+61 4{8}',  # Australia
            'CA': '+1 {10}',   # Canada
            'DEFAULT': '+60 1{8}'  # Default to Malaysia
        }
    
    def clean_name_for_email(self, name):
        """
        @function clean_name_for_email - 清理姓名用于生成邮箱
        @param {str} name - 原始姓名
        @returns {str} 清理后的姓名
        """
        if not name:
            return ""
        
        # 移除特殊字符，保留字母和空格
        cleaned = re.sub(r'[^a-zA-Z\s]', '', str(name))
        # 转换为小写并移除多余空格
        cleaned = ' '.join(cleaned.lower().split())
        return cleaned
    
    def generate_email(self, name, existing_emails):
        """
        @function generate_email - 生成邮箱地址
        @param {str} name - 客户姓名
        @param {set} existing_emails - 已存在的邮箱集合
        @returns {str} 生成的邮箱地址
        """
        if not name:
            return ""
        
        cleaned_name = self.clean_name_for_email(name)
        if not cleaned_name:
            return ""
        
        name_parts = cleaned_name.split()
        
        # 生成多种邮箱前缀格式
        prefixes = []
        
        if len(name_parts) >= 2:
            first_name = name_parts[0]
            last_name = name_parts[-1]
            
            # 常见格式
            prefixes.extend([
                f"{first_name}.{last_name}",
                f"{first_name}{last_name}",
                f"{first_name}_{last_name}",
                f"{last_name}.{first_name}",
                f"{first_name[0]}{last_name}",
                f"{first_name}{last_name[0]}",
                f"{first_name}.{last_name}{random.randint(1, 999):02d}",
                f"{first_name}{random.randint(1, 99):02d}"
            ])
        else:
            # 单个名字的情况
            single_name = name_parts[0]
            prefixes.extend([
                single_name,
                f"{single_name}{random.randint(1, 999):02d}",
                f"{single_name}_{random.randint(1, 99):02d}"
            ])
        
        # 尝试生成唯一邮箱
        for prefix in prefixes:
            for provider in self.email_providers:
                email = f"{prefix}@{provider}"
                if email not in existing_emails:
                    existing_emails.add(email)
                    return email
        
        # 如果都重复，添加随机数字
        base_prefix = prefixes[0] if prefixes else "user"
        for i in range(100):
            email = f"{base_prefix}{random.randint(1000, 9999)}@{random.choice(self.email_providers)}"
            if email not in existing_emails:
                existing_emails.add(email)
                return email
        
        return f"user{random.randint(10000, 99999)}@gmail.com"
    
    def infer_gender(self, name):
        """
        @function infer_gender - 基于姓名推断性别
        @param {str} name - 客户姓名
        @returns {str} 推断的性别 (Male/Female)
        """
        if not name:
            return "Female"  # 默认值
        
        name_lower = str(name).lower()
        name_parts = name_lower.split()
        
        # 检查名字中是否包含明显的性别指示词
        for part in name_parts:
            if part in self.male_names:
                return "Male"
            elif part in self.female_names:
                return "Female"
        
        # 基于名字的首字母或常见模式进行推断
        first_name = name_parts[0] if name_parts else ""
        
        # 一些简单的规则
        if first_name.endswith(('a', 'ia', 'ina', 'ana', 'lyn', 'lynn')):
            return "Female"
        elif first_name.endswith(('er', 'on', 'an', 'en')):
            return "Male"
        
        # 随机分配，但偏向女性（基于旅游统计）
        return random.choices(["Female", "Male"], weights=[0.6, 0.4])[0]
    
    def detect_country_from_passport(self, passport):
        """
        @function detect_country_from_passport - 从护照号码推断国家
        @param {str} passport - 护照号码
        @returns {str} 国家代码
        """
        if not passport or str(passport).lower() in ['nan', '', '0']:
            return 'DEFAULT'
        
        passport_str = str(passport).upper()
        
        # 基于护照号码格式推断国家
        if passport_str.startswith(('K', 'H', 'G')):
            return 'HK'  # Hong Kong
        elif passport_str.startswith(('P', 'PA', 'PB', 'PC')):
            return 'PH'  # Philippines
        elif passport_str.startswith(('M', 'MA', 'MB', 'MJ')):
            return 'MY'  # Malaysia or Macau
        elif passport_str.startswith('TT'):
            return 'JP'  # Japan
        elif len(passport_str) == 9 and passport_str.isdigit():
            return 'TW'  # Taiwan
        elif passport_str.startswith('E') and len(passport_str) == 9:
            return 'CN'  # China
        else:
            return 'DEFAULT'
    
    def generate_phone_number(self, country_code):
        """
        @function generate_phone_number - 生成手机号码
        @param {str} country_code - 国家代码
        @returns {str} 生成的手机号码
        """
        format_template = self.country_phone_formats.get(country_code, self.country_phone_formats['DEFAULT'])
        
        # 解析格式模板
        if '{' in format_template:
            prefix, suffix = format_template.split('{')
            digit_count = int(suffix.split('}')[0])
            random_digits = ''.join([str(random.randint(0, 9)) for _ in range(digit_count)])
            return prefix + random_digits
        else:
            return format_template
    
    def process_customer_data(self):
        """
        @function process_customer_data - 处理客户数据，补充缺失字段
        @returns {pd.DataFrame} 处理后的数据
        """
        try:
            # 读取CSV文件
            df = pd.read_csv(self.csv_file)
            print(f"读取到 {len(df)} 条客户记录")
            
            existing_emails = set()
            
            # 处理每条记录
            for index, row in df.iterrows():
                # 生成邮箱
                if pd.isna(row['email']) or row['email'] == '':
                    email = self.generate_email(row['Name'], existing_emails)
                    df.at[index, 'email'] = email
                
                # 推断性别
                if pd.isna(row['gendre']) or row['gendre'] == '':
                    gender = self.infer_gender(row['Name'])
                    df.at[index, 'gendre'] = gender
                
                # 生成手机号码
                if pd.isna(row['contact']) or row['contact'] == '':
                    country = self.detect_country_from_passport(row['passport'])
                    phone = self.generate_phone_number(country)
                    df.at[index, 'contact'] = phone
            
            print("数据处理完成")
            return df
            
        except Exception as e:
            print(f"处理数据时出错: {str(e)}")
            return None

    def group_by_month(self, df):
        """
        @function group_by_month - 按旅行月份分组数据
        @param {pd.DataFrame} df - 客户数据
        @returns {dict} 按月份分组的数据字典
        """
        month_groups = {
            'February': [],
            'March': [],
            'April': []
        }

        for index, row in df.iterrows():
            travel_date = str(row['Travel Date'])
            if 'February' in travel_date:
                month_groups['February'].append(row)
            elif 'March' in travel_date:
                month_groups['March'].append(row)
            elif 'April' in travel_date:
                month_groups['April'].append(row)

        # 转换为DataFrame
        for month in month_groups:
            if month_groups[month]:
                month_groups[month] = pd.DataFrame(month_groups[month])
            else:
                month_groups[month] = pd.DataFrame()

        return month_groups

    def create_excel_document(self, df):
        """
        @function create_excel_document - 创建Excel备案文档
        @param {pd.DataFrame} df - 处理后的客户数据
        @returns {bool} 是否成功创建
        """
        try:
            # 按月份分组
            month_groups = self.group_by_month(df)

            # 创建Excel写入器
            with pd.ExcelWriter(self.output_excel, engine='openpyxl') as writer:

                for month, month_df in month_groups.items():
                    if len(month_df) == 0:
                        continue

                    # 准备工作表数据
                    sheet_data = month_df.copy()

                    # 重新排列列顺序
                    columns_order = ['Name', 'gendre', 'email', 'contact', 'Date of birth', 'passport', 'Travel Date', 'Destination']
                    sheet_data = sheet_data[columns_order]

                    # 重命名列标题为更专业的格式
                    sheet_data.columns = ['Full Name', 'Gender', 'Email Address', 'Contact Number', 'Date of Birth', 'Passport Number', 'Travel Date', 'Destination']

                    # 创建工作表名称 (简化名称以避免Excel限制)
                    sheet_name = f"{month} 2025"

                    # 写入工作表 (从第4行开始，为标题留出空间)
                    sheet_data.to_excel(writer, sheet_name=sheet_name, index=False, startrow=3)

                    # 获取工作表对象以添加标题
                    worksheet = writer.sheets[sheet_name]

                    # 添加标题和信息
                    worksheet['A1'] = f"Travel Insurance Backup Document - {month} 2025"
                    worksheet['A2'] = f"Total Customers: {len(month_df)}"
                    worksheet['A3'] = ""  # 空行分隔

                    # 设置标题样式
                    from openpyxl.styles import Font, Alignment
                    title_font = Font(bold=True, size=14)
                    subtitle_font = Font(bold=True, size=12)

                    worksheet['A1'].font = title_font
                    worksheet['A2'].font = subtitle_font

                    # 设置列宽
                    column_widths = [25, 10, 30, 18, 15, 18, 15, 20]
                    columns = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H']
                    for i, width in enumerate(column_widths):
                        worksheet.column_dimensions[columns[i]].width = width

                    # 设置表头样式
                    header_font = Font(bold=True)
                    for col in columns:
                        cell = worksheet[f"{col}4"]  # 第4行是表头
                        if cell.value:
                            cell.font = header_font

            print(f"Excel文档已创建: {self.output_excel}")
            return True

        except Exception as e:
            print(f"创建Excel文档时出错: {str(e)}")
            return False

    def update_csv_file(self, df):
        """
        @function update_csv_file - 更新CSV文件
        @param {pd.DataFrame} df - 处理后的数据
        @returns {bool} 是否成功更新
        """
        try:
            df.to_csv(self.output_csv, index=False, encoding='utf-8')
            print(f"CSV文件已更新: {self.output_csv}")
            return True
        except Exception as e:
            print(f"更新CSV文件时出错: {str(e)}")
            return False

    def generate_documents(self):
        """
        @function generate_documents - 生成完整的保险备案文档
        @returns {bool} 是否成功生成
        """
        print("开始生成旅游责任险备案文档...")
        print("=" * 80)

        # 1. 处理客户数据
        print("步骤1: 处理客户数据，补充缺失字段")
        df = self.process_customer_data()

        if df is None:
            print("数据处理失败")
            return False

        # 2. 创建Excel文档
        print("\n步骤2: 创建分页Excel文档")
        excel_success = self.create_excel_document(df)

        # 3. 更新CSV文件
        print("\n步骤3: 更新完整的CSV文件")
        csv_success = self.update_csv_file(df)

        if excel_success and csv_success:
            print("\n" + "=" * 80)
            print("文档生成完成!")
            print(f"Excel文档: {self.output_excel}")
            print(f"CSV文件: {self.output_csv}")

            # 显示统计信息
            month_groups = self.group_by_month(df)
            print(f"\n客户分布:")
            for month, month_df in month_groups.items():
                print(f"  {month} 2025: {len(month_df)} 位客户")

            return True
        else:
            print("文档生成过程中出现错误")
            return False

def main():
    """
    @function main - 主函数
    @returns {None}
    """
    generator = InsuranceDocumentGenerator()
    generator.generate_documents()

if __name__ == "__main__":
    main()
