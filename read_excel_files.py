#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@file read_excel_files.py - 读取Excel文件内容的工具脚本
@function 读取指定的Excel文件并显示其内容
"""

import pandas as pd
import os
import sys

def read_excel_file(file_path):
    """
    @function read_excel_file - 读取Excel文件并显示内容
    @param {str} file_path - Excel文件路径
    @returns {bool} 是否成功读取
    """
    try:
        print(f"\n=== 正在读取文件: {os.path.basename(file_path)} ===")
        
        # 读取Excel文件的所有工作表
        excel_file = pd.ExcelFile(file_path)
        print(f"工作表列表: {excel_file.sheet_names}")
        
        # 读取每个工作表
        for sheet_name in excel_file.sheet_names:
            print(f"\n--- 工作表: {sheet_name} ---")
            df = pd.read_excel(file_path, sheet_name=sheet_name)
            
            print(f"数据形状: {df.shape} (行数: {df.shape[0]}, 列数: {df.shape[1]})")
            print(f"列名: {list(df.columns)}")
            
            # 显示前几行数据
            print("\n前5行数据:")
            print(df.head())
            
            # 如果数据不多，显示所有数据
            if df.shape[0] <= 20:
                print("\n完整数据:")
                print(df.to_string(index=False))
            else:
                print(f"\n数据太多，仅显示前5行。总共有 {df.shape[0]} 行数据。")
                
        return True
        
    except Exception as e:
        print(f"读取文件 {file_path} 时出错: {str(e)}")
        return False

def main():
    """
    @function main - 主函数，读取所有Excel文件
    @returns {None}
    """
    # Excel文件路径列表
    excel_files = [
        r"C:\Users\<USER>\Downloads\new\bookinglist_2025-03-13-000000-2025-06-13-235959_20250613145942_28209_4076.xlsx",
        r"C:\Users\<USER>\Downloads\new\bookinglist_2025-03-13-000000-2025-06-13-235959_20250613150003_28209_1160.xlsx"
    ]
    
    print("开始读取Excel文件...")
    
    for file_path in excel_files:
        if os.path.exists(file_path):
            read_excel_file(file_path)
        else:
            print(f"文件不存在: {file_path}")
    
    print("\n=== 所有文件读取完成 ===")

if __name__ == "__main__":
    main()
