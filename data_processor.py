#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@file data_processor.py - Excel数据提取并更新CSV文件的处理脚本
@function 从Excel文件提取客户信息，清理数据，并更新insurance.csv文件
"""

import pandas as pd
import random
import os
from datetime import datetime, date
import numpy as np

class DataProcessor:
    """
    @class DataProcessor - 数据处理类
    """
    
    def __init__(self):
        """
        @function __init__ - 初始化数据处理器
        """
        self.destinations = ['Ipoh', 'Johor Bahru', 'Kuala Lumpur', 'Port Dickson', 'Penang']
        self.excel_files = [
            r"C:\Users\<USER>\Downloads\new\bookinglist_2025-03-13-000000-2025-06-13-235959_20250613145942_28209_4076.xlsx",
            r"C:\Users\<USER>\Downloads\new\bookinglist_2025-03-13-000000-2025-06-13-235959_20250613150003_28209_1160.xlsx"
        ]
        self.csv_file = r"C:\Users\<USER>\Downloads\new\insurance.csv"
        
    def clean_name(self, last_name, first_name):
        """
        @function clean_name - 清理并合并姓名
        @param {str} last_name - 姓氏
        @param {str} first_name - 名字
        @returns {str} 合并后的完整姓名
        """
        # 处理空值
        last_name = str(last_name).strip() if pd.notna(last_name) else ""
        first_name = str(first_name).strip() if pd.notna(first_name) else ""
        
        # 移除 'nan' 字符串
        if last_name.lower() == 'nan':
            last_name = ""
        if first_name.lower() == 'nan':
            first_name = ""
            
        # 合并姓名
        if last_name and first_name:
            return f"{first_name} {last_name}"
        elif first_name:
            return first_name
        elif last_name:
            return last_name
        else:
            return ""
    
    def clean_passport(self, passport1, passport2):
        """
        @function clean_passport - 清理护照号码
        @param {str} passport1 - 第一个护照号码字段
        @param {str} passport2 - 第二个护照号码字段
        @returns {str} 清理后的护照号码
        """
        # 处理第一个护照号码
        p1 = str(passport1).strip() if pd.notna(passport1) else ""
        if p1.lower() in ['nan', '00', '']:
            p1 = ""
            
        # 处理第二个护照号码
        p2 = str(passport2).strip() if pd.notna(passport2) else ""
        if p2.lower() in ['nan', '00', '']:
            p2 = ""
        
        # 返回非空的护照号码
        if p1:
            return p1
        elif p2:
            return p2
        else:
            return ""
    
    def generate_random_date(self):
        """
        @function generate_random_date - 生成2025年2-4月的随机日期
        @returns {str} 格式化的日期字符串
        """
        # 定义月份和对应的天数
        months_days = {
            2: 28,  # 2025年2月
            3: 31,  # 2025年3月
            4: 30   # 2025年4月
        }
        
        # 随机选择月份
        month = random.choice([2, 3, 4])
        day = random.randint(1, months_days[month])
        
        # 格式化日期
        date_obj = date(2025, month, day)
        return date_obj.strftime("%B %d")  # 例如: "March 15"
    
    def format_birth_date(self, birth_date):
        """
        @function format_birth_date - 格式化出生日期
        @param {str|datetime} birth_date - 出生日期
        @returns {str} 格式化的出生日期
        """
        if pd.isna(birth_date):
            return ""
            
        try:
            if isinstance(birth_date, str):
                # 尝试解析字符串日期
                if '/' in birth_date:
                    parts = birth_date.split('/')
                    if len(parts) == 3:
                        year, month, day = parts
                        return f"{year}/{month}/{day}"
                return birth_date
            elif isinstance(birth_date, (datetime, pd.Timestamp)):
                return birth_date.strftime("%Y/%m/%d")
            else:
                return str(birth_date)
        except:
            return str(birth_date)
    
    def extract_customer_data(self):
        """
        @function extract_customer_data - 从Excel文件提取客户数据
        @returns {list} 客户数据列表
        """
        all_customers = []
        
        for file_path in self.excel_files:
            if not os.path.exists(file_path):
                print(f"文件不存在: {file_path}")
                continue
                
            try:
                print(f"正在处理文件: {os.path.basename(file_path)}")
                df = pd.read_excel(file_path)
                
                for index, row in df.iterrows():
                    # 提取姓名
                    last_name = row.get('10010002-Last name', '')
                    first_name = row.get('10010003-First name', '')
                    full_name = self.clean_name(last_name, first_name)
                    
                    if not full_name:  # 跳过没有姓名的记录
                        continue
                    
                    # 提取出生日期
                    birth_date = self.format_birth_date(row.get('10010006-Date of birth', ''))
                    
                    # 提取护照号码
                    passport1 = row.get('10412114-Passport or MyKad Number', '')
                    passport2 = row.get('10412117-Passport or MyKad Number', '')
                    passport = self.clean_passport(passport1, passport2)
                    
                    # 生成随机旅行日期和目的地
                    travel_date = self.generate_random_date()
                    destination = random.choice(self.destinations)
                    
                    # 创建客户记录
                    customer = {
                        'Name': full_name,
                        'gendre': '',  # 性别字段留空
                        'email': '',   # 邮箱字段留空
                        'contact': '', # 联系电话留空
                        'Date of birth': birth_date,
                        'passport': passport,
                        'Travel Date': travel_date,
                        'Destination': destination
                    }
                    
                    all_customers.append(customer)
                    
                print(f"从 {os.path.basename(file_path)} 提取了 {len([c for c in all_customers if c])} 条记录")
                
            except Exception as e:
                print(f"处理文件 {file_path} 时出错: {str(e)}")
        
        return all_customers
    
    def remove_duplicates(self, customers):
        """
        @function remove_duplicates - 去除重复的客户记录
        @param {list} customers - 客户数据列表
        @returns {list} 去重后的客户数据列表
        """
        seen = set()
        unique_customers = []
        
        for customer in customers:
            # 使用姓名+出生日期作为唯一标识
            key = f"{customer['Name']}|{customer['Date of birth']}"
            
            if key not in seen:
                seen.add(key)
                unique_customers.append(customer)
        
        print(f"去重前: {len(customers)} 条记录")
        print(f"去重后: {len(unique_customers)} 条记录")
        
        return unique_customers
    
    def update_csv_file(self, customers):
        """
        @function update_csv_file - 更新CSV文件
        @param {list} customers - 客户数据列表
        @returns {bool} 是否成功更新
        """
        try:
            # 创建DataFrame
            df = pd.DataFrame(customers)

            # 确保列的顺序正确
            columns_order = ['Name', 'gendre', 'email', 'contact', 'Date of birth', 'passport', 'Travel Date', 'Destination']
            df = df[columns_order]

            # 尝试保存到新的CSV文件
            new_csv_file = r"C:\Users\<USER>\Downloads\new\insurance_updated.csv"

            try:
                # 先尝试保存到原文件
                df.to_csv(self.csv_file, index=False, encoding='utf-8')
                print(f"成功更新原CSV文件: {self.csv_file}")
            except PermissionError:
                # 如果原文件被占用，保存到新文件
                df.to_csv(new_csv_file, index=False, encoding='utf-8')
                print(f"原文件被占用，已保存到新文件: {new_csv_file}")
                print("请关闭原CSV文件后，将新文件重命名为原文件名")

            print(f"总共写入 {len(customers)} 条客户记录")

            return True

        except Exception as e:
            print(f"更新CSV文件时出错: {str(e)}")
            return False
    
    def process_data(self):
        """
        @function process_data - 执行完整的数据处理流程
        @returns {bool} 处理是否成功
        """
        print("开始数据处理...")
        print("=" * 60)
        
        # 1. 提取客户数据
        print("步骤1: 从Excel文件提取客户数据")
        customers = self.extract_customer_data()
        
        if not customers:
            print("没有提取到任何客户数据")
            return False
        
        # 2. 去除重复记录
        print("\n步骤2: 去除重复记录")
        unique_customers = self.remove_duplicates(customers)
        
        # 3. 更新CSV文件
        print("\n步骤3: 更新CSV文件")
        success = self.update_csv_file(unique_customers)
        
        if success:
            print("\n" + "=" * 60)
            print("数据处理完成!")
            print(f"CSV文件已更新: {self.csv_file}")
        
        return success

def main():
    """
    @function main - 主函数
    @returns {None}
    """
    processor = DataProcessor()
    processor.process_data()

if __name__ == "__main__":
    main()
